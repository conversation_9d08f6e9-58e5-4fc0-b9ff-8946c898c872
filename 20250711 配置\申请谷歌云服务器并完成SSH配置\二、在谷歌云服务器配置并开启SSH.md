#### 第1步：更新软件包列表

这是在安装任何新软件之前都必须做的第一步。它会从 Ubuntu 的软件源服务器同步最新的可用软件包信息。

```bash
sudo apt-get update
```

#### 第2步：安装 OpenSSH 服务器

这是解决你连接问题的核心步骤。

```bash
sudo apt-get install openssh-server -y
```

- `openssh-server` 就是 SSH 服务的软件包。
- `-y` 参数会自动对安装过程中的所有提示回答 "yes"，省去手动确认的麻烦。

安装完成后，`apt` 通常会自动启动 `sshd` 服务并将其设置为开机自启

#### 第3步：安装基础工具（强烈推荐）

既然连 `less` 都没有，说明系统缺少很多基础工具。我们顺便把它们装上，方便以后管理。`net-tools` 提供了像 `ifconfig` 这样的网络命令，`ufw` 是防火墙工具，`nano` 是文本编辑工具。

```bash
sudo apt-get install less net-tools ufw nano -y
```

#### 第4步：验证 SSH 服务是否已在运行

现在，我们再来运行之前的检查命令。这次它应该能找到了。

```bash
sudo systemctl status ssh
```

**注意：** 在 Ubuntu/Debian 系统中，SSH 服务的名字通常是 `ssh`，而不是 `sshd`。我之前的指令有误，非常抱歉。`ssh` 是正确的服务名。

你应该能看到绿色的 `Active: active (running)`。如果不是，请手动启动它：

```bash
sudo systemctl start ssh
sudo systemctl enable ssh
```

#### 第5步：配置防火墙（可选但推荐）

既然我们已经安装了 `ufw`，现在可以正确地配置它了。

```bash
# 允许SSH连接
sudo ufw allow ssh

# 启用防火墙
sudo ufw enable
```

当被问到 "Command may disrupt existing ssh connections. Proceed with operation (y|n)?" 时，输入 `y` 并回车。因为我们当前是在串行端口操作，所以不会中断连接。