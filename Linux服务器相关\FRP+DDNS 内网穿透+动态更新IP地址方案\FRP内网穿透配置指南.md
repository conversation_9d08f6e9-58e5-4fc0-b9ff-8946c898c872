
## 1. 环境概述

### 1.1 服务端环境
- **服务器**: 公网服务器，IP: **************
- **系统**: Linux
- **FRP版本**: 0.62.1
- **部署位置**: /home/<USER>/frp_0.62.1_linux_amd64/

### 1.2 客户端环境
- **系统**: Ubuntu 24.04
- **FRP版本**: 0.62.1
- **部署方式**: Docker容器 (snowdreamtech/frpc:0.62.1)
- **容器名**: 1Panel-frpc-YOXA
- **配置位置**: /opt/1panel/apps/frpc/frpc/data/frpc.toml

## 2. 当前配置详情

### 2.1 服务端(frps)配置

```toml
# 绑定IP，表示监听所有网络接口
bindAddr = "0.0.0.0"
# 绑定端口，客户端连接到此端口
bindPort = 10794

# 添加认证方式，提高安全性
auth.method = "token"
auth.token = "ZHWok949!@%+"

# 日志配置
log.to = "/home/<USER>/frp_0.62.1_linux_amd64/frps.log"
log.level = "info"
log.maxDays = 3

# Web管理界面配置
webServer.addr = "0.0.0.0"
webServer.port = 10795
webServer.user = "zhwok10794"
webServer.password = "ZHWok949!@%*+"

# HTTP服务端口
vhostHTTPPort = 8880

# HTTPS服务端口
vhostHTTPSPort = 4443
```

### 2.2 客户端(frpc)配置

```toml
# 服务器地址和端口
serverAddr = "**************"
serverPort = 10794

# 认证信息 (与服务端一致)
auth.method = "token"
auth.token = "ZHWok949!@%+"

# 日志设置
log.to = "./frpc.log"
log.level = "info"
log.maxDays = 3

# Web服务代理配置 - HTTP方式
[[proxies]]
name = "web-service"
type = "http"
localIP = "127.0.0.1"
localPort = 3000
customDomains = ["**************"]
```

### 2.3 当前服务状态

- **frps服务**: 运行中，可通过systemd管理
- **frpc服务**: 在Docker容器中运行，由1Panel管理
- **代理状态**: HTTP代理"web-service"在线，代理内网3000端口服务
- **访问方式**: http://**************:8880
- **管理界面**: http://**************:10795 (用户名: zhwok10794, 密码: ZHWok949!@%*+)

### 2.4 遇到的问题与解决方案

- **问题**: frpc无法连接到frps服务器
  **原因**: 配置中serverPort使用了错误的端口(9490而非10794)
  **解决**: 修正serverPort为10794

- **问题**: 无法访问代理的Web服务
  **原因**: 服务器防火墙阻止了8880端口
  **解决**: 开放防火墙端口 `sudo ufw allow 8880/tcp`

- **问题**: frps服务无法绑定到80端口
  **原因**: 非root用户无法绑定低于1024的特权端口
  **解决**: 使用高于1024的端口(8880)替代80端口

## 3. 增量配置指南

### 3.1 添加新的HTTP代理

如果您想要添加另一个HTTP服务（例如在内网的8080端口），只需在frpc.toml中添加以下配置：

```toml
[[proxies]]
name = "another-web-service"
type = "http"
localIP = "127.0.0.1"
localPort = 8080
customDomains = ["**************"]
```

访问方式：`http://**************:8880`，frps会根据Host头部将请求路由到不同的内网服务。

### 3.2 添加TCP代理（如SSH服务）

如果您想要暴露内网的SSH服务：

```toml
[[proxies]]
name = "ssh-service"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 6000
```

访问方式：`ssh -p 6000 username@**************`

### 3.3 添加HTTPS代理

如果您有内网HTTPS服务：

```toml
[[proxies]]
name = "secure-web"
type = "https"
localIP = "127.0.0.1"
localPort = 8443
customDomains = ["**************"]
```

访问方式：`https://**************:4443`

### 3.4 添加UDP代理

如果您需要代理UDP服务：

```toml
[[proxies]]
name = "game-server"
type = "udp"
localIP = "127.0.0.1"
localPort = 7000
remotePort = 7000
```

### 3.5 使用子域名访问多个Web服务

在frps.toml中添加：

```toml
subDomainHost = "example.com"  # 替换为您的域名
```

然后在frpc.toml中配置：

```toml
[[proxies]]
name = "api-service"
type = "http"
localIP = "127.0.0.1"
localPort = 3000
subdomain = "api"  # 将创建api.example.com
```

访问方式：`http://api.example.com:8880`

## 4. 高级配置示例

### 4.1 启用加密和压缩

```toml
[[proxies]]
name = "secure-service"
type = "tcp"
localIP = "127.0.0.1"
localPort = 3000
remotePort = 6000
transport.useEncryption = true
transport.useCompression = true
```

### 4.2 配置健康检查

```toml
[[proxies]]
name = "web-service"
type = "http"
localIP = "127.0.0.1"
localPort = 3000
customDomains = ["**************"]
healthCheck.type = "http"
healthCheck.path = "/health"
healthCheck.intervalSeconds = 10
healthCheck.maxFailed = 3
healthCheck.timeoutSeconds = 3
```

### 4.3 限制带宽使用

```toml
[[proxies]]
name = "limited-service"
type = "tcp"
localIP = "127.0.0.1"
localPort = 3000
remotePort = 6000
transport.bandwidthLimit = "1MB"
transport.bandwidthLimitMode = "client"
```

### 4.4 配置HTTP请求/响应头

```toml
[[proxies]]
name = "web-service"
type = "http"
localIP = "127.0.0.1"
localPort = 3000
customDomains = ["**************"]
hostHeaderRewrite = "localhost"
requestHeaders.set.X-From-Where = "frp"
responseHeaders.set.Access-Control-Allow-Origin = "*"
```

## 5. 安全增强配置

### 5.1 限制允许的端口范围（frps端）

```toml
# 限制可用端口范围
allowPorts = [
  { start = 6000, end = 7000 },
  { single = 8000 },
  { single = 9000 }
]

# 限制每个客户端最大端口数
maxPortsPerClient = 5
```

### 5.2 启用TLS加密

在frpc.toml中：

```toml
transport.tls.enable = true
```

在frps.toml中：

```toml
transport.tls.force = true
```

## 6. 管理操作指南

### 6.1 服务端(frps)管理

**启动frps服务**:
```bash
sudo systemctl start frps
```

**停止frps服务**:
```bash
sudo systemctl stop frps
```

**查看frps状态**:
```bash
sudo systemctl status frps
```

**查看frps日志**:
```bash
tail -f /home/<USER>/frp_0.62.1_linux_amd64/frps.log
```

**修改frps配置**:
```bash
nano /home/<USER>/frp_0.62.1_linux_amd64/frps.toml
sudo systemctl restart frps
```

### 6.2 客户端(frpc)管理

**查看frpc容器状态**:
```bash
sudo docker ps | grep frpc
```

**查看frpc日志**:
```bash
sudo docker logs 1Panel-frpc-YOXA
# 或
sudo docker exec 1Panel-frpc-YOXA cat /root/frpc.log
```

**修改frpc配置**:
```bash
nano /opt/1panel/apps/frpc/frpc/data/frpc.toml
sudo docker restart 1Panel-frpc-YOXA
```

**检查frpc连接状态**:
访问 http://**************:10795/static/#/ 查看代理状态

### 6.3 防火墙配置

**在frps服务器上开放必要端口**:
```bash
sudo ufw allow 10794/tcp  # frps绑定端口
sudo ufw allow 10795/tcp  # Web管理界面端口
sudo ufw allow 8880/tcp   # HTTP代理端口
sudo ufw allow 4443/tcp   # HTTPS代理端口
sudo ufw allow 6000/tcp   # 如果配置了SSH代理
sudo ufw reload
```

## 7. 故障排查指南

### 7.1 连接问题

如果frpc无法连接到frps：
- 检查frps服务器IP和端口是否正确
- 确认token是否一致
- 检查网络连接：`telnet ************** 10794`
- 查看frpc日志：`sudo docker logs 1Panel-frpc-YOXA`

### 7.2 代理不工作

如果代理显示为在线但无法访问：
- 确认本地服务正在运行：`curl http://localhost:3000`
- 检查frps服务器防火墙是否允许代理端口（8880、4443等）
- 尝试使用TCP代理替代HTTP代理进行测试

### 7.3 权限问题

如果遇到权限问题（如绑定特权端口失败）：
- 使用非特权端口（>1024）
- 使用端口转发：`iptables -t nat -A PREROUTING -p tcp --dport 80 -j REDIRECT --to-port 8880`

## 8. 完整配置模板

### 8.1 完整的frpc.toml配置模板

```toml
# 服务器地址和端口
serverAddr = "**************"
serverPort = 10794

# 认证信息
auth.method = "token"
auth.token = "ZHWok949!@%+"

# 日志设置
log.to = "./frpc.log"
log.level = "info"
log.maxDays = 3

# Web应用代理 (HTTP)
[[proxies]]
name = "web-service"
type = "http"
localIP = "127.0.0.1"
localPort = 3000
customDomains = ["**************"]

# API服务代理 (HTTP)
[[proxies]]
name = "api-service"
type = "http"
localIP = "127.0.0.1"
localPort = 8080
customDomains = ["**************"]
locations = ["/api"]

# SSH服务代理 (TCP)
[[proxies]]
name = "ssh-service"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 6000

# 数据库代理 (TCP)
[[proxies]]
name = "db-service"
type = "tcp"
localIP = "127.0.0.1"
localPort = 3306
remotePort = 6001
```

### 8.2 完整的frps.toml配置模板

```toml
# 基本配置
bindAddr = "0.0.0.0"
bindPort = 10794

# 认证配置
auth.method = "token"
auth.token = "ZHWok949!@%+"

# Web管理界面
webServer.addr = "0.0.0.0"
webServer.port = 10795
webServer.user = "zhwok10794"
webServer.password = "ZHWok949!@%*+"

# HTTP/HTTPS服务支持
vhostHTTPPort = 8880
vhostHTTPSPort = 4443

# 日志配置
log.to = "/home/<USER>/frp_0.62.1_linux_amd64/frps.log"
log.level = "info"
log.maxDays = 3

# 安全配置
allowPorts = [
  { start = 6000, end = 7000 },
  { single = 8000 },
  { single = 9000 }
]
maxPortsPerClient = 10

# 子域名配置
# subDomainHost = "example.com"

# 启用Prometheus监控
enablePrometheus = true
```

## 9. 注意事项与最佳实践

1. **定期更新FRP版本**以获取最新功能和安全修复
2. **使用强密码和Token**避免未授权访问
3. **启用TLS加密**增强传输安全性
4. **定期检查日志**发现潜在问题
5. **备份配置文件**防止意外丢失
6. **为重要服务配置健康检查**确保服务可用性
7. **合理设置带宽限制**避免资源过度使用
8. **使用非特权端口**避免权限问题
9. **配置防火墙**只开放必要端口
10. **使用Docker管理frpc**简化部署和维护

## 10. 常见使用场景

1. **远程访问内网Web应用**：通过HTTP/HTTPS代理
2. **远程SSH访问内网服务器**：通过TCP代理
3. **远程访问内网数据库**：通过TCP代理
4. **内网游戏服务器公网访问**：通过UDP代理
5. **微信公众号/小程序开发**：通过HTTP代理实现内网开发环境的公网访问
6. **远程桌面访问**：通过TCP代理RDP端口(3389)
7. **内网监控系统公网访问**：通过HTTP代理
8. **多个内网服务通过同一端口访问**：使用HTTP代理+子域名/路径区分