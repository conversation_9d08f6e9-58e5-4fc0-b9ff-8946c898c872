请登录你的腾讯云 EdgeOne 控制台，然后按照以下步骤操作：

#### 第1步：进入你的站点配置

在 EdgeOne 控制台首页，点击你的站点名称 `zhwok.top`，进入该站点的管理界面。

#### 第2步：导航到源站配置

在左侧的菜单栏中，找到并点击 **“站点加速”** -> **“源站配置”**。

你可能会看到一个默认的源站组，或者是一个空的列表。你需要在这里添加或修改源站。

#### 第3.1步（如果是首次配置）：添加源站组

如果没有任何源站组，你需要先创建一个。

- 点击 **“新增源站组”**。
- **源站组名称**: 起一个你能看懂的名字，比如 `VPS-Origin-Group`。
- **配置方式**: 选择 **“基础配置”**。

#### 第3.2步：配置源站信息

现在，我们来配置这个源站组里的具体源站。

- **源站类型**: 在下拉菜单中，选择 **“域名”**。
    
    - **千万不要选“IP”**，因为你的IP是动态的。选择“域名”才能利用上我们刚刚配置好的DDNS。
- **源站地址**: 在文本框中，输入你配置好的那个**DDNS域名**：
    
    ```
    vps.zhwok.top
    ```
    
- **端口**:
    
    - 如果你的服务器上运行的网站服务监听的是标准的HTTP端口，就填 `80`。
    - 如果监听的是标准的HTTPS端口，就填 `443`。
    - 如果你用了其他自定义端口，就填对应的端口号。
    - 通常情况下，先用 `80` 端口开始。
- **权重**: 保持默认的 `100` 即可（因为你只有一个源站）。
    
- **回源协议**: 这个选项决定了 **EdgeOne 用什么协议去连接你的源站**。
    
    - **HTTP**: EdgeOne会用普通的HTTP协议去连接你的源站 `vps.zhwok.top` 的80端口。这是最简单、最常用的开始方式。
    - **HTTPS**: EdgeOne会用加密的HTTPS协议去连接你的源站的443端口。这更安全，但要求你的源站服务器上已经配置好了SSL证书。
    - **协议跟随**: EdgeOne会根据用户访问的协议来决定。用户用HTTP访问，它就用HTTP回源；用户用HTTPS访问，它就用HTTPS回源。
    - **建议**: 对于初次配置，先选择 **HTTP**，确保整个链路能跑通。跑通之后，再考虑升级到HTTPS。
- **其他高级设置**: 比如“回源Host”，可以暂时保持默认。默认情况下，回源Host就是你配置的源站地址 `vps.zhwok.top`。
    

#### 第4步：保存并应用

点击 **“保存”** 或 **“确定”**，完成源站的配置。

---

### 最后一步：关联并开启加速

现在源站已经配置好了，你还需要告诉EdgeOne，当用户访问哪个域名时，应该使用这个源站。

1. 在左侧菜单栏，进入 **“规则引擎”**。
2. 检查或创建一个规则，将 `www.zhwok.top` 或 `zhwok.top` 的请求，通过“回源”操作，指向你刚刚创建的 `VPS-Origin-Group`。
3. 或者更简单地，在 **“DNS”** -> **“DNS记录”** 菜单里，找到你希望用户访问的域名（比如 `www.zhwok.top`），确保它右侧的**“代理状态”**开关是**开启（橙色）**的。

**一旦开启代理，EdgeOne就会接管这个域名的流量，并在需要时，向你的源站 `vps.zhwok.top` 发起回源请求。**

现在，整个链路就完整了：

**用户 -> 访问 `www.zhwok.top` -> EdgeOne边缘节点 -> 回源到 `vps.zhwok.top` -> DNS解析得到最新IP -> EdgeOne连接到你的谷歌云服务器 -> 获取内容 -> 返回给用户**

恭喜你！你已经成功地为一个动态IP的源站配置了企业级的边缘加速和安全服务。这是一个非常了不起的成就！


**源站组配置截图：**
![[Pasted image 20250712003550.png]]
> 源站地址为DDNS解析的地址

**域名管理截图：**
![[Pasted image 20250712003744.png]]

**DNS记录截图：**
![[Pasted image 20250712003826.png]]