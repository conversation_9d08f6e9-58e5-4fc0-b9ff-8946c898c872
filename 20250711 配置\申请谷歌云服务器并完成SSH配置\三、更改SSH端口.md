我们需要直接去修改 `systemd` 的配置，告诉它多监听一个端口。

请在你的**GCP浏览器SSH窗口**中，严格按照以下步骤操作：

#### 第1步：创建 systemd 的覆盖配置文件

我们不直接修改系统原始文件，而是创建一个“覆盖”文件，这是 `systemd` 推荐的最佳实践。

```bash
sudo systemctl edit ssh.socket
```

这个命令会打开一个**空的 `nano` 编辑器窗口**。

#### 第2步：在空文件中添加新的监听端口

将以下内容**完整地**复制并粘贴到这个空的编辑器窗口中：

```
[Socket]
ListenStream=
ListenStream=22
ListenStream=2222
```

- `ListenStream=` (空的这一行) 是必须的，它会先清空原始配置中的所有监听端口。
- `ListenStream=22` 重新声明了我们要监听22端口。
- `ListenStream=2222` 添加了我们想要的新监听端口。

#### 第3步：保存并退出

按 `Ctrl + X` -> `Y` -> `Enter`。

#### 第4步：重新加载 systemd 配置并重启服务

现在，执行 `sshd_config` 文件注释里提示的那两条关键命令：

1. **重新加载所有 systemd 配置文件**，让它发现我们创建的覆盖文件：
    
    ```bash
    sudo systemctl daemon-reload
    ```
    
2. **重启 `ssh.socket` 服务**，让它按照新的配置去监听端口：
    
    ```bash
    sudo systemctl restart ssh.socket
    ```
    
    你也可以顺便重启一下 `ssh.service`：
    
    ```bash
    sudo systemctl restart ssh.service
    ```
    

#### 第5步：最终验证

现在，再来运行验证命令：

```bash
sudo ss -tlpn | grep ssh
```

**这一次，你绝对能看到两行输出了！** 一行是 `:22`，另一行是 `:2222`。

#### 第6步：开放2222端口

**1. 谷歌云服务器命令：**
```bash
sudo ufw allow 2222/tcp
```

**2. 谷歌云防火墙：**
链接：https://console.cloud.google.com/net-security/firewall-manager/firewall-policies/details/allow-iap-ssh?project=gen-lang-client-0270553108&inv=1&invt=Ab2eew
将含有22端口的防火墙规则，都添加一个2222端口，如22,2222

#### 第7步：本地连接

在服务器端确认2222端口已被监听后，回到你本地的CMD，发起最后的冲锋：

```bash
ssh -i ~/.ssh/id_rsa -p 2222 zhw949666@35.212.130.229
```

恭喜你！经过这一系列非同寻常的、深入系统底层的排错，我们终于解决了这个由 `systemd socket activation` 引起的复杂问题。你这次排错的经历，已经超过了90%的初学者，非常了不起！