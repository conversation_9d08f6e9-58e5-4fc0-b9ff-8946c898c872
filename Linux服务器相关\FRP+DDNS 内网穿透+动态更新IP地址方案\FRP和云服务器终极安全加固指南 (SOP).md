
## 1. 核心安全理念

安全不是单一的功能，而是一个层层递进的体系。我们的目标是构建“纵深防御”，即使一层被突破，后续的层次依然能有效抵御入侵。核心原则如下：

*   **最小权限原则**：只开放绝对必要的端口，只授予服务和用户完成其工作所需的最小权限。
*   **隐藏攻击面**：将非必要服务（如管理后台）从公网隐藏，增加攻击者发现和利用的难度。
*   **加密所有通信**：确保敏感数据在传输过程中是加密的，防止被窃听。
*   **自动化防御**：利用工具自动识别和阻断恶意行为。

---

## 2. 操作前置条件

本指南假设你已经拥有一个可以正常工作的FRP穿透系统，现在需要对其进行安全加固。

---

## 3. 安全加固步骤

### 阶段一：云服务器操作系统加固 (加固堡垒外墙)

这是暴露在公网上的第一道防线，必须坚不可摧。

#### 1. 强化SSH登录安全 (最重要的第一步)

**目标**：彻底杜绝SSH暴力破解攻击。

**A. 确认密钥登录有效**

> **警告**: 在禁用密码登录前，**必须100%确认**你可以通过SSH密钥成功登录，否则你将被永久锁在服务器外！

在你**本地电脑**上打开终端，运行：
```powershell
# 将 <密钥路径>, <用户名>, <服务器IP> 替换为你的实际信息
ssh -i <密钥路径> <用户名>@<服务器IP>
```
如果无需输入密码即可登录，方可继续。

**B. 修改SSH配置文件**

登录到你的云服务器，执行以下操作：
```bash
# 1. 使用sudo权限打开配置文件
sudo nano /etc/ssh/sshd_config
```
滚动到文件最底部，粘贴以下整个配置块。它会自动覆盖任何默认设置。
```ini
# --- START: MY CUSTOM SECURE SSH CONFIG ---
# 更改默认端口为2222，有效躲避99%的自动化扫描
Port 2222

# 禁止root用户直接远程登录
PermitRootLogin no

# 强制只使用公钥认证，安全性最高
PubkeyAuthentication yes

# 禁用所有形式的密码认证，彻底杜绝暴力破解
PasswordAuthentication no
ChallengeResponseAuthentication no
# --- END: MY CUSTOM SECURE SSH CONFIG ---
```
保存并退出 (`Ctrl+X`, `Y`, `Enter`)。

**C. 更新云平台防火墙**

这是至关重要的一步！
- 登录你的GCP控制台 -> "VPC 网络" -> "防火墙"。
- 找到你之前创建的防火墙规则。
- 在允许的TCP端口列表中，**添加新的SSH端口 `2222`**，同时可以**删除旧的`22`端口**。

**D. 重启SSH服务并验证**
```bash
# 在服务器上重启SSH服务
sudo systemctl restart sshd
```
在**本地电脑**上，打开一个**【新】**终端, 使用新端口连接：
```powershell
# 注意，必须加上 -p 2222 参数！
ssh -i <密钥路径> <用户名>@<服务器IP> -p 2222
```
连接成功后，你的SSH就变得非常安全了。

#### 2. 安装并启用 Fail2Ban (自动化防御)

**目标**：自动封禁持续尝试恶意登录的IP地址。
```bash
# 在云服务器上执行
sudo apt update && sudo apt install -y fail2ban
sudo systemctl enable --now fail2ban
```
> **提示**: 安装即用，无需配置，它已默认开始保护你的SSH服务。

---

### 阶段二：FRP服务自身加固 (加固隧道和管理后台)

#### 1. 强制TLS加密FRP通信

**目标**：加密`frps`和`frpc`之间的所有数据流，防止中间人攻击。

**A. 修改服务端 `frps.toml`**
```bash
# 在云服务器上执行
sudo nano /home/<USER>/frp/frps.toml
```
在配置文件中，找到 `auth` 部分，并在其下方添加一行 `transport.tls.force = true`。正确的模板应如下所示：
```toml
# frps.toml
bindAddr = "0.0.0.0"
bindPort = 10794

auth.method = "token"
auth.token = "<你的超长随机令牌>"

# 强制启用TLS加密
transport.tls.force = true

# ... 其他配置 ...
```

**B. 修改客户端 `frpc.toml`**
在你的**内网服务器**上，编辑 `frpc.toml` 文件，添加 `transport.tls.enable = true`。
```toml
# frpc.toml
serverAddr = "frp.your-domain.com"
serverPort = 10794

auth.method = "token"
auth.token = "<与服务端完全相同的令牌>"

# 启用TLS以匹配服务端
transport.tls.enable = true

# ... 你的 [[proxies]] 配置 ...
```

**C. 重启FRP服务**
```bash
# 在云服务器上
sudo systemctl restart frps

# 在内网服务器上
sudo systemctl restart frpc
```

#### 2. 隐藏并安全访问FRP管理后台

**目标**：将Web管理后台从公网移除，只通过加密的SSH隧道访问。

**A. 修改 `frps.toml` 将后台绑定到本地**
```bash
# 在云服务器上执行
sudo nano /home/<USER>/frp/frps.toml
```
找到 `[webServer]` 配置块，将 `addr` 的值从 `"0.0.0.0"` 修改为 `"127.0.0.1"`。
```toml
# frps.toml
# ...
[webServer]
addr = "127.0.0.1" # <-- 修改这里
port = 10795
user = "<你的后台用户名>"
password = "<你的后台密码>"
```
修改后，重启frps服务： `sudo systemctl restart frps`。

**B. 通过SSH隧道安全访问**
在你的**本地电脑**终端，运行以下命令来建立安全隧道：
```powershell
# ssh -L [本地端口]:[目标地址]:[目标端口] [user]@[host] -p [port]
ssh -L 10795:127.0.0.1:10795 -i <你的密钥路径> <用户名>@<服务器IP> -p 2222
```
隧道建立后（终端显示服务器欢迎信息），打开你**本地浏览器**，访问 `http://localhost:10795`，即可安全登录管理后台。

---

### 阶段三：配置操作系统防火墙 `ufw` (家里的门锁)

**目标**：确保操作系统层面只开放了我们明确知道且需要的端口。
```bash
# 在云服务器上执行

# 1. 检查ufw状态 (如果之前没有配置,先执行一遍)
# sudo ufw status

# 2. 明确允许我们需要的端口
sudo ufw allow 2222/tcp      # 允许新的SSH端口
sudo ufw allow 10794/tcp     # 允许frp的核心通信端口
# sudo ufw allow 10795/tcp   # 注意: 管理后台已绑定到本地, 无需通过ufw开放
sudo ufw allow 8880/tcp      # 允许frp的HTTP服务端口

# 3. (如果ufw未开启) 开启ufw防火墙
# sudo ufw enable

# 4. 再次查看最终状态，确保规则正确
sudo ufw status
```

---

## 4. 最终安全自查清单

完成以上所有步骤后，请对照此清单进行最终确认：

- [ ] **SSH**: 能否通过 `2222` 端口和SSH密钥成功登录？旧的`22`端口是否已无法连接？ ⛔ o5zowb
- [ ] **Fail2Ban**: `sudo systemctl status fail2ban` 是否显示 `active (running)`?
- [ ] **FRP Token**: `frps.toml` 和 `frpc.toml` 中的 `auth.token` 是否已更换为超长随机字符串？ ⛔ o5zowb
- [ ] **FRP TLS**: 两端配置文件是否都已添加 `transport.tls` 配置？`frpc`服务能否正常连接`frps`？
- [ ] **FRP Dashboard**: 能否通过SSH隧道在本地 `http://localhost:10795` 成功访问管理后台？直接用公网IP访问是否已失败？
- [ ] **UFW**: `sudo ufw status` 的输出是否只包含了你明确允许的那几个端口？
- [ ] **GCP防火墙**: 网页控制台的防火墙规则是否也只开放了对应的端口？

**恭喜你！完成以上所有步骤后，你的个人网络穿透系统已具备强大的安全防护能力。**