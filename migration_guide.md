# FRP + DDNS-GO 安全迁移指南

## 🎯 迁移目标
将谷歌云服务器上的 FRP 和 DDNS-GO 服务安全迁移到腾讯云服务器，确保内网服务器连接不中断。

## 📋 当前配置分析

### 谷歌云服务器 (35.212.194.155)
- **FRP服务器**: 端口 9688, Dashboard 7855
- **DDNS-GO**: 端口 4588, 管理域名 vps.zhwok.top
- **认证Token**: `mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC`

### 内网服务器 (通过 vps.zhwok.top:4088 访问)
- **FRP客户端**: Docker容器运行，连接到 vps.zhwok.top:9688
- **主要代理**: HTTPS(443) 和 SSH(4088)

### 腾讯云服务器 (**************)
- **目标**: 完全替代谷歌云服务器功能

## 🚀 安全迁移步骤

### 第一阶段：腾讯云服务器部署

1. **上传并执行部署脚本**
   ```bash
   # 上传脚本到腾讯云服务器
   scp frp_migration_script.sh ubuntu@**************:/tmp/
   
   # SSH连接到腾讯云服务器
   ssh ubuntu@************** -p 2222
   
   # 执行部署脚本
   sudo chmod +x /tmp/frp_migration_script.sh
   sudo /tmp/frp_migration_script.sh
   ```

2. **验证服务状态**
   ```bash
   # 检查服务状态
   sudo systemctl status frps
   sudo systemctl status ddns-go
   
   # 检查端口监听
   sudo netstat -tlnp | grep -E "(9688|4588)"
   
   # 查看日志
   sudo journalctl -u frps -n 20
   sudo journalctl -u ddns-go -n 20
   ```

### 第二阶段：连接测试（关键步骤）

⚠️ **重要**: 这一步是确保不失联的关键！

1. **临时测试连接**
   ```bash
   # 在内网服务器上，备份原配置
   sudo cp /opt/1panel/apps/frpc/frpc/data/frpc.toml /opt/1panel/apps/frpc/frpc/data/frpc.toml.backup
   
   # 临时修改配置文件，将服务器地址改为腾讯云IP
   sudo nano /opt/1panel/apps/frpc/frpc/data/frpc.toml
   ```

2. **修改配置内容**
   ```toml
   # 将这一行：
   serverAddr = "vps.zhwok.top"
   
   # 临时改为：
   serverAddr = "**************"  # 腾讯云IP
   ```

3. **重启frpc容器测试**
   ```bash
   # 重启frpc容器
   sudo docker restart 71aee2b14912
   
   # 检查连接状态
   sudo docker logs 71aee2b14912 --tail 20
   ```

4. **验证连接**
   - 如果看到 "login to server success" 说明连接成功
   - 尝试通过 **************:4088 SSH连接内网服务器
   - 如果连接失败，立即恢复原配置：
     ```bash
     sudo cp /opt/1panel/apps/frpc/frpc/data/frpc.toml.backup /opt/1panel/apps/frpc/frpc/data/frpc.toml
     sudo docker restart 71aee2b14912
     ```

### 第三阶段：DDNS配置

1. **配置DDNS-GO**
   ```bash
   # 访问DDNS-GO管理界面
   http://**************:4588
   ```

2. **配置域名解析**
   - 登录DDNS-GO管理界面
   - 配置域名: `vps.zhwok.top`
   - 目标IP: `**************`
   - 保存并启动自动更新

3. **等待DNS生效**
   ```bash
   # 检查DNS解析
   nslookup vps.zhwok.top
   dig vps.zhwok.top
   ```

### 第四阶段：最终切换

1. **确认DNS生效后，修改内网配置**
   ```bash
   # 将配置改回域名
   sudo nano /opt/1panel/apps/frpc/frpc/data/frpc.toml
   ```

2. **最终配置**
   ```toml
   serverAddr = "vps.zhwok.top"  # 现在指向腾讯云
   serverPort = 9688
   auth.method = "token"
   auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"
   ```

3. **重启并验证**
   ```bash
   sudo docker restart 71aee2b14912
   sudo docker logs 71aee2b14912 --tail 20
   ```

### 第五阶段：清理

1. **确认一切正常后，关闭谷歌云服务器**
2. **删除备份文件**
   ```bash
   sudo rm /opt/1panel/apps/frpc/frpc/data/frpc.toml.backup
   ```

## 🔧 故障排除

### 常见问题

1. **FRP连接失败**
   - 检查防火墙设置
   - 验证Token是否正确
   - 查看服务器日志

2. **DDNS更新失败**
   - 检查域名服务商API配置
   - 验证网络连接
   - 查看DDNS-GO日志

3. **内网服务器失联**
   - 立即恢复备份配置
   - 重启frpc容器
   - 检查谷歌云服务器状态

### 紧急恢复

如果迁移过程中出现问题：

```bash
# 1. 恢复内网服务器配置
sudo cp /opt/1panel/apps/frpc/frpc/data/frpc.toml.backup /opt/1panel/apps/frpc/frpc/data/frpc.toml
sudo docker restart 71aee2b14912

# 2. 确保谷歌云服务器正常运行
# 3. 重新分析问题并调整方案
```

## 📞 联系信息

- 腾讯云服务器: `ubuntu@**************:2222`
- 内网服务器: `<EMAIL>:4088`
- FRP Dashboard: `http://**************:7855` (用户名: zhw)
- DDNS-GO管理: `http://**************:4588`

## ✅ 迁移检查清单

- [ ] 腾讯云服务器部署完成
- [ ] FRP服务正常运行
- [ ] DDNS-GO服务正常运行 🆔 5nhdzj
- [ ] 防火墙规则配置完成
- [ ] 临时IP连接测试成功
- [ ] DDNS配置完成
- [ ] DNS解析生效
- [ ] 域名连接测试成功
- [ ] 内网服务器最终配置完成
- [ ] 谷歌云服务器安全关闭

---

**⚠️ 重要提醒**: 整个迁移过程中，请保持谷歌云服务器运行，直到确认腾讯云服务器完全正常工作！
