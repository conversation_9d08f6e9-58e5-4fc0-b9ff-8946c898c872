### 第1步：在你的本地电脑生成 SSH 密钥对

这个操作在你自己的电脑（Windows、macOS 或 Linux）上完成，而不是在服务器上。

#### 打开你的终端

- **macOS**: 打开 “终端” (Terminal) 应用。
- **Linux**: 打开任何终端模拟器 (如 GNOME Terminal, Konsole 等)。
- **Windows**: 推荐使用 **PowerShell** 或 **WSL (Windows Subsystem for Linux)**。CMD 也可以，但 PowerShell 功能更强大。

#### 运行密钥生成命令

在终端中输入以下命令并回车：

```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

我们来解释一下这个命令的各个部分：

- `ssh-keygen`: 这是生成密钥的程序。
- `-t rsa`: 指定要创建的密钥类型。`RSA` 是最常用和兼容性最好的类型。
- `-b 4096`: 指定密钥的强度（位数）。`4096` 位非常安全，是目前的推荐标准。
- `-C "your_username@your_machine_name"`: 这是密钥的注释（Comment）。它不会影响功能，但有助于你识别这个密钥是干什么用的。通常格式是 `用户名@主机名`，例如 `john@my-macbook`。你也可以写成你的邮箱，比如 `"<EMAIL>"`。

#### 交互式设置

运行命令后，程序会问你几个问题：

1. **“Enter file in which to save the key...” (输入文件保存位置)**
    
    ```
    Enter file in which to save the key (/Users/<USER>/.ssh/id_rsa):
    ```
    
    这里是问你把密钥文件存放在哪里。
    
    - **推荐做法**：直接按 **回车 (Enter)** 键。这会将密钥保存在默认的 `.ssh` 目录下，文件名为 `id_rsa` (私钥) 和 `id_rsa.pub` (公钥)。这是标准位置，大多数 SSH 工具会自动查找这里。
    - **自定义名称**：如果你想给它一个特定的名字（例如，为了区分不同云服务商的密钥），你可以输入一个完整的路径，比如 `/Users/<USER>/.ssh/google_cloud_key`。 **记住你输入的文件名！**
2. **“Enter passphrase (empty for no passphrase):” (输入口令)**
    
    ```
    Enter passphrase (empty for no passphrase):
    ```
    
    这是为你的私钥设置一个额外的保护密码。
    
    - **强烈推荐**：**输入一个强大且你能记住的口令！** 这意味着即使有人偷走了你的私钥文件，没有这个口令，他也无法使用它。
    - 输入时屏幕上不会显示任何字符（包括星号），这是正常的安全措施。输入后按回车。
3. **“Enter same passphrase again:” (再次输入口令)**
    
    ```
    Enter same passphrase again:
    ```
    
    再次输入刚才的口令以确认。
    

完成后，你会看到一个漂亮的密钥指纹和图案，这表示密钥对已经成功生成！

```
Your identification has been saved in /Users/<USER>/.ssh/id_rsa.
Your public key has been saved in /Users/<USER>/.ssh/id_rsa.pub.
The key fingerprint is:
SHA256:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx your_username@your_machine_name
The key's randomart image is:
+---[RSA 4096]----+
|        ..       |
|       . .       |
|        E .      |
|     . o o .     |
|    . * S . .    |
|   . = = = .     |
|    * +.+ .      |
|   . B=B .       |
|    O*O+...      |
+----[SHA256]-----+
```

现在，在你的 `.ssh` 文件夹里，多了两个文件：

- `id_rsa` (或者你自定义的名字): **私钥**。**绝对不能泄露给任何人！**
- `id_rsa.pub` (或者你自定义的名字.pub): **公钥**。这个是你可以安全地分享和上传到服务器的文件。

---

### 第2步：将公钥添加到 Google Cloud VM

现在要把你的 "钥匙" (公钥) 交给 "门锁" (服务器)。

1. **复制你的公钥内容**  
    在你的本地终端，使用以下命令来显示公-钥文件的内容：
    
    - 在 macOS 上:
        
        ```bash
        cat ~/.ssh/id_rsa.pub | pbcopy
        ```
        
        (这个命令会直接将公钥内容复制到剪贴板)
    - 在 Linux 上:
        
        ```bash
        cat ~/.ssh/id_rsa.pub
        ```
        
        (然后手动复制终端输出的所有内容)
    - 在 Windows (PowerShell) 上:
        
        ```bash
        Get-Content $env:USERPROFILE\.ssh\id_rsa.pub | Set-Clipboard
        ```
        
        (这个命令会直接将公钥内容复制到剪贴板)
    
    公钥内容看起来像这样，是一整行很长的文本：  
    `ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC... your_username@your_machine_name`
    
2. **登录 Google Cloud 控制台**  
    a. 打开 [Google Cloud 控制台](https://www.google.com/url?sa=E&q=https%3A%2F%2Fconsole.cloud.google.com%2F)。  
    b. 导航到 **Compute Engine -> VM 实例**。  
    c. 点击你的虚拟机实例名称，进入详情页面。  
    d. 点击顶部的 **“修改”**（EDIT）按钮。  
    e. 向下滚动到 **“安全和访问权限”** > **“SSH 密钥”** 部分。  
    f. 点击 **“显示和修改”**，然后点击 **“添加 SSH 密钥”**。  
    g. 在弹出的文本框中，**粘贴你刚刚复制的公钥内容**。  
    h. GCP 会自动在右侧的“用户名”字段填入一个名字。**记住这个用户名**，登录时需要。  
    i. 点击页面底部的 **“保存”**。
    

GCP 会花一小会儿时间来更新实例并应用新的 SSH 密钥。

---

### 第3步：使用你的私钥连接到虚拟机

万事俱备，现在可以开门了！

1. **获取虚拟机的公网 IP**  
    在 VM 实例列表中，找到你的虚拟机，复制其**“外部 IP”**地址。
    
2. **打开你的本地终端**  
    使用以下命令进行连接：
    
    ```bash
    ssh [用户名]@[虚拟机的公网IP]
    ```
    
    - `[用户名]`: 就是刚才在 GCP 添加密钥时，GCP 自动生成的那个用户名。
    - `[虚拟机的公网IP]`: 你刚刚复制的外部 IP 地址。
    
    **示例：**
    
    ```bash
    ssh gcp_user_example@************
    ```
    
    - 如果你生成密钥时使用了**自定义文件名**，比如 `google_cloud_key`，你需要用 `-i` 参数指定私钥路径：
        
        ```bash
        ssh -i ~/.ssh/google_cloud_key gcp_user_example@************
        ```
        
3. **输入口令**
    
    - 如果是第一次连接这个服务器，SSH 会问你一个安全问题：
        
        ```
        The authenticity of host '************ (************)' can't be established.
        Are you sure you want to continue connecting (yes/no/[fingerprint])?
        ```
        
        输入 `yes` 并回车。这会将服务器的指纹保存到你本地，下次就不会再问了。
    - 接着，系统会提示你输入为私钥设置的口令：
        
        ```
        Enter passphrase for key '/Users/<USER>/.ssh/id_rsa':
        ```
        
        输入你在第1步中设置的那个**口令**，然后回车。

**恭喜！** 你现在应该已经成功登录到你的 Google Cloud 虚拟机了，终端的提示符会变成类似 `gcp_user_example@instance-name:~$` 的样子。