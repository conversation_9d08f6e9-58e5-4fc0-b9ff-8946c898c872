### 一、下载并解压FRP包

你可以直接在你的谷歌云服务器上通过 `wget` 命令来下载它。

1. **复制下载链接**  
    在 frp 的 GitHub Release 页面，右键点击 `frp_0.63.0_linux_amd64.tar.gz` 这个链接，选择“复制链接地址”。
    
2. **登录你的服务器并下载**  
    通过 SSH 登录到你的服务器，然后执行：
    
    ```bash
    # 创建一个工作目录（好习惯）
    mkdir ~/frp_install
    cd ~/frp_install
    
    # 使用 wget 下载，粘贴你刚才复制的链接
    wget https://github.com/fatedier/frp/releases/download/v0.63.0/frp_0.63.0_linux_amd64.tar.gz
    ```
    
3. **解压文件**
    
    ```bash
    tar -zxvf frp_0.63.0_linux_amd64.tar.gz
    ```
    
4. **进入解压后的目录**
    
    ```bash
    # 目录名通常是 frp_版本号_系统_架构
    cd frp_0.63.0_linux_amd64
    ```
    


### 二、在拥有公网IP的机器配置frps.toml
1. 编辑frps.toml文件
```bash
nano frps.toml
```

2. 写入配置
```bash
# ==============================
# FRP 服务器端（frps.ini）配置
# ==============================

# 绑定监听地址（默认 `0.0.0.0` 代表监听所有 IP）
bindAddr = "0.0.0.0"

#  服务器监听端口（客户端需要通过该端口连接 FRP 服务器）
bindPort = 9688

# HTTP 端口（用于内网 HTTP 代理穿透）
vhostHTTPPort = 8080

# HTTPS 端口（用于内网 HTTPS 代理穿透）
vhostHTTPSPort = 8443

# 子域名支持
# 可以通过 `subDomainHost` 解析动态子域名
# 例如：如果 `subDomainHost` 配置为 "example.com"
# 那么客户端可以使用 `test.example.com` 访问内网服务
# 如果你没有域名或不使用此功能，请删除此行！
# 如果你要用IP直连例如:*********:8848，就把这行删掉，不要配置！
subDomainHost = "zhwok.top"  # 请替换为你的真实域名

# =============================================
# Web 控制台（Dashboard）配置
# =============================================

# 监控界面监听地址（`127.0.0.1` 代表所有 仅本地 可访问）
webServer.addr = "127.0.0.1"

# Web 管理面板端口（可在浏览器访问，默认 7500）
# 你可以通过 `http://你的公网IP:7500` 访问 FRP 管理面板
webServer.port = 7855

# Web 控制台管理账号（可自定义）
webServer.user = "zhw"

# Web 控制台密码（请自行修改）
webServer.password = "Zhwok949!@%+"

# =============================================
# 身份验证（Authentication）配置
# =============================================

#  认证方式（防止未经授权的客户端连接）
# 目前 FRP 支持 `token` 和 `oidc` 方式，我们选用token
auth.method = "token"

#  Token 认证（客户端需要匹配相同 token 才能连接）
# 通俗来说就是密码，写一个你能记住的，尽量长一点
# 示例: 123-abc-123abc
auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"
```

3. 用systemd服务启动frps
    ```bash
    # 创建 `systemd` 服务文件
    
    sudo nano /etc/systemd/system/frps.service
    ```

	```bash
# 粘贴以下内容
[Unit]
Description=FRP Server Service
After=network.target

[Service]
Type=simple

# 注意修改为你的frps和配置文件的实际绝对路径
ExecStart=/home/<USER>/frp_install/frp_0.63.0_linux_amd64/frps -c /home/<USER>/frp_install/frp_0.>Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
    ```
    
    **注意**:
    
    - 请**务必**将 `ExecStart` 中的路径 `/home/<USER>/frp_install/...` 替换成你服务器上 `frps` 和 `frps.toml` 的**绝对路径**。你可以用 `pwd` 命令在相应目录查看绝对路径。
    - `User=root` 如果你的 `bindPort` 小于1024（比如80），则必须用root用户启动。你的端口是`9688`，理论上可以用普通用户，但用root启动更简单。
5. **加载并启动服务**:
    
    ```bash
    sudo systemctl daemon-reload
    sudo systemctl start frps
    sudo systemctl enable frps
    ```
    
6. **查看状态和日志**:
    
    - `sudo systemctl status frps`
    - `sudo journalctl -u frps -f` (实时查看日志)

### 三、在需要穿透的机器配置frpc.toml
```bash
serverAddr = "vps.zhwok.top"
serverPort = 9688

auth.method = "token"
auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"

# tls
#transport.tls.certFile = "/etc/frp/ssl/client.crt"
#transport.tls.keyFile = "/etc/frp/ssl/client.key"
#transport.tls.trustedCaFile = "/etc/frp/ssl/ca.crt"
    ```

