
## 1. 核心目标与系统架构

### 1.1. 目标

本文旨在指导一个新手，在拥有**一台动态公网IP的云服务器**和**一台内网服务器**的情况下，通过 `DDNS`、`Cloudflare` 和 `FRP` 技术，实现使用一个**固定的域名**安全、稳定地访问部署在**内网服务器**上的服务。

### 1.2. 系统架构

```
你 (终端用户)
      |
      | 1. 访问 https://your-domain.com
      V
[ Cloudflare DNS & Proxy ] <-----------------------+
      | 2. 解析域名:                              | 5. IP变化时, DDNS脚本自动更新A记录
      |    - your-domain.com -> CNAME -> frp.your-domain.com |
      |    - frp.your-domain.com -> A -> [动态变化的服务器IP]        |
      V                                         |
[ 谷歌云服务器 (IP动态) ] <-----------------------+
      | - 运行 frps (FRP服务端)                     |
      | - 运行 DDNS 脚本 (监控与更新IP)            |
      |                                         |
      | 3. frps 根据访问的域名, 将请求通过隧道转发给目标frpc     |
      V                                         |
[ 内网服务器 (家里/办公室) ] <--------------------+ 4. 与frps保持一个长期、稳定的隧道连接
      | - 运行 frpc (FRP客户端)                       |
      V
[ 内网的具体服务 (网站, SSH, 数据库...) ]
```


---

## 2. 准备工作 (Prerequisites)

在开始之前，请确保你已拥有以下三项资源：

1.  **一个你自己的域名**：例如 `your-domain.com`。可以从GoDaddy, NameSilo, 阿里云等平台购买。
2.  **一台云服务器**：本文以谷歌云 (GCP) 为例，操作系统为`Debian`或`Ubuntu`。**拥有动态公网IP**。
3.  **一台内网服务器**：可以是家里的电脑、树莓派、NAS等。

---

## 3. 作业步骤

### 阶段一：Cloudflare 配置 (搭建云端基础)

这是所有工作的第一步，为我们的域名和动态IP建立联系的桥梁。

1.  **托管域名到Cloudflare**
    - 登录/注册 [Cloudflare](https://dash.cloudflare.com/)。
    - 添加你的站点 (域名)，选择免费计划。
    - Cloudflare会提供两个NS服务器地址。请登录你的域名购买平台，将域名的DNS服务器修改为Cloudflare提供的这两个地址。等待几分钟到几小时生效。

2.  **配置DNS记录 (核心)**
    - 进入你的域名管理页面，点击左侧的 "DNS"。
    - **创建A记录 (用于DDNS)**：这条记录将被脚本自动更新，是动态IP的锚点。
      - **类型**: `A`
      - **名称**: `frp` (你也可以用其他名字, 如 `proxy`, `tunnel`)
      - **IPv4 地址**: `*******` (这是一个临时的、无效的IP，脚本会自动更新它)
      - **代理状态**: **灰色云 (仅限DNS)**。**必须是灰色！**
    - **创建CNAME记录 (用于访问)**：这条记录让你用主域名访问服务。
      - **类型**: `CNAME`
      - **名称**: `@` (代表你的根域名, 即`your-domain.com`)
      - **目标**: `frp.your-domain.com` (必须与上面A记录的完整名称一致)
      - **代理状态**: **橙色云 (已代理)**。**推荐是橙色！**

3.  **获取API凭证 (授权脚本)**
    - **获取区域ID (Zone ID)**：
      - 在域名的“概述” (Overview) 页面，往下滚动，找到右侧的 API 部分。
      - 复制 **区域ID (Zone ID)** 并保存好。
    - **创建API令牌 (Token)**：
      - 点击右上角头像 -> "我的个人资料" -> 左侧 "API令牌" -> "创建令牌"。
      - 找到 “编辑区域DNS” (Edit zone DNS) 模板，点击 “使用模板”。
      - 在 **区域资源 (Zone Resources)** 部分，选择你的域名。
      - 点击 “继续以显示摘要”，然后 “创建令牌”。
      - **立即复制生成的令牌字符串并妥善保存**，它只会出现一次！

### 阶段二：谷歌云服务器配置 (部署`frps`与`DDNS`脚本)

在这台拥有动态IP的服务器上，我们将部署FRP的服务端和负责更新IP的DDNS脚本。

1.  **系统初始化与安装依赖**
    - SSH登录你的谷歌云服务器。
    - 执行以下命令，更新系统并安装所有必要工具：
      ```bash
      sudo apt update && sudo apt install -y git dnsutils wget
      ```

2.  **配置服务器防火墙**
    - 在GCP控制台，进入 "VPC 网络" -> "防火墙"。
    - 创建一条新的防火墙规则，允许外部流量访问FRP将要使用的端口。
      - **名称**: `frp-server-ports` (或任意你喜欢的名字)
      - **流量方向**: 入站 (Ingress)
      - **目标**: 网络中的所有实例 (或为你的服务器设置一个特定标签并选用)
      - **来源IP地址范围**: `0.0.0.0/0` (允许任何IP访问)
      - **协议和端口**:
        - 勾选 `TCP`，在输入框中填入以下端口 (用逗号分隔)，这些端口必须与你后续 `frps.toml` 的配置一致。
        - **示例**: `10794, 10795, 8880`

3.  **安装并配置DDNS脚本**
    ```bash
    # 1. 回到主目录并克隆脚本仓库
    cd ~
    git clone https://github.com/fire1ce/DDNS-Cloudflare-Bash.git

    # 2. 进入目录并创建配置文件
    cd DDNS-Cloudflare-Bash
    nano update-cloudflare-dns.conf
    ```
    - **3. 粘贴并修改以下模板内容到 `update-cloudflare-dns.conf` 文件中:**
      ```ini
      # [DDNS SCRIPT CONFIG]
      # 应当使用的IP类型: internal (内网IP) 或 external (公网IP)
      what_ip="external"
      # 需要更新的DNS A记录 (与Cloudflare上的A记录名称一致)
      dns_record="frp.your-domain.com"
      # 你从Cloudflare获取的区域ID (Zone ID)
      zoneid="<在此处粘贴你的Zone ID>"
      # 你从Cloudflare获取的API令牌 (API Token)
      cloudflare_zone_api_token="<在此处粘贴你的API Token>"
      # DNS记录是否使用Cloudflare代理 (此处应与A记录保持一致，为false)
      proxied="false"
      # TTL值 (通常保持默认)
      ttl=120
      # Telegram通知 (不需要则保持no)
      notify_me_telegram="no"
      telegram_chat_id=""
      telegram_bot_API_Token=""
      ```
    - **4. 保存并退出** (`Ctrl+X`, `Y`, `Enter`)。
    - **5. 授权并首次运行测试**
      ```bash
      chmod +x update-cloudflare-dns.sh
      ./update-cloudflare-dns.sh
      ```
      > 如果看到 `Success!` 或 `UNCHANGED` 的输出，代表配置成功！
    - **6. 设置定时任务 (自动化运行)**
      ```bash
      crontab -e
      ```
      - 在打开的编辑器末尾，添加下面这行 (请将`<你的用户名>`替换为你的实际用户名，如`zhw`)：
        ```crontab
        */5 * * * * /home/<USER>/DDNS-Cloudflare-Bash/update-cloudflare-dns.sh >/dev/null 2>&1
        ```
      - 保存并退出。DDNS部分已全部完成！

4.  **安装并配置FRP服务端 (`frps`)**
    - **1. 下载并解压FRP** (请到 [FRP Releases](https://github.com/fatedier/frp/releases) 页面查找最新版本号替换)
      ```bash
      cd ~
      wget https://github.com/fatedier/frp/releases/download/v0.62.1/frp_0.62.1_linux_amd64.tar.gz
      tar -zxvf frp_0.62.1_linux_amd64.tar.gz
      mv frp_0.62.1_linux_amd64 frp
      cd frp
      ```
    - **2. 创建 `frps.toml` 配置文件**
      ```bash
      rm frps.toml frpc.toml frpc_full.toml # 删除自带的示例
      nano frps.toml
      ```
    - **3. 粘贴并修改以下模板内容到 `frps.toml` 文件中:**
      ```toml
      # [FRPS CONFIG]
      # 绑定IP和端口, 供frpc客户端连接
      bindAddr = "0.0.0.0"
      bindPort = 10794

      # 认证令牌, frpc必须使用相同的令牌才能连接
      auth.method = "token"
      auth.token = "<设置一个你自己的强密码>"

      # HTTP虚拟主机端口, 所有来自8880端口的HTTP请求将被frp处理
      vhostHTTPPort = 8880

      # Web管理界面配置
      webServer.addr = "0.0.0.0"
      webServer.port = 10795
      webServer.user = "<设置管理后台用户名>"
      webServer.password = "<设置管理后台密码>"
      ```
    - **4. 保存并退出**。

5.  **将 `frps` 配置为系统服务 (开机自启)**
    - **1. 创建systemd服务文件**
      ```bash
      sudo nano /etc/systemd/system/frps.service
      ```
    - **2. 粘贴以下内容** (注意, 请将`<你的用户名>`替换为你的实际用户名!)
      ```ini
      [Unit]
      Description=FRP Server
      After=network.target

      [Service]
      Type=simple
      User=<你的用户名>
      Restart=on-failure
      RestartSec=5s
      ExecStart=/home/<USER>/frp/frps -c /home/<USER>/frp/frps.toml

      [Install]
      WantedBy=multi-user.target
      ```
    - **3. 启动服务并设置开机自启**
      ```bash
      sudo systemctl enable frps
      sudo systemctl start frps
      ```
    - **4. 检查服务状态**
      ```bash
      sudo systemctl status frps
      ```
      > 看到 `active (running)` 即代表服务端部署成功。

### 阶段三：内网服务器配置 (部署`frpc`)

1.  **安装FRP客户端 (`frpc`)**
    - 与服务端类似，下载对应你内网服务器系统架构的FRP版本并解压。
      - `linux_amd64` (标准64位Linux)
      - `linux_arm64` (树莓派等ARM架构64位系统)
    - 解压后进入目录。

2.  **创建 `frpc.toml` 配置文件**
    ```bash
    # 进入frp目录, 删除不需要的文件
    rm frps.toml frpc.toml frps_full.toml
    nano frpc.toml
    ```
    - **粘贴并修改以下模板内容到 `frpc.toml` 文件中:**
      ```toml
      # [FRPC CONFIG]
      # 服务器地址, 必须是我们的DDNS域名
      serverAddr = "frp.your-domain.com"
      # 服务器端口, 必须与frps的bindPort一致
      serverPort = 10794

      # 认证配置, 必须与frps的token一致
      auth.method = "token"
      auth.token = "<填写与frps中完全相同的密码>"

      # --- ↓↓↓ 在这里定义你想要穿透的内网服务 ↓↓↓ ---

      # 示例1: 穿透一个本地的Web服务
      [[proxies]]
      name = "my-web-app"
      type = "http"
      localIP = "127.0.0.1"    # 你的Web服务监听的IP
      localPort = 8080         # 你的Web服务监听的端口
      customDomains = ["your-domain.com"] # 使用你的根域名来访问
      
      # 示例2: 穿透SSH服务
      [[proxies]]
      name = "my-ssh-service"
      type = "tcp"
      localIP = "127.0.0.1"
      localPort = 22
      remotePort = 10722 # 在云服务器上暴露的端口, 需要在防火墙放行
      ```

3.  **将 `frpc` 配置为系统服务 (开机自启)**
    - 与 `frps` 步骤完全相同, 在**内网服务器**上创建一个 `frpc.service` 文件。
      ```bash
      sudo nano /etc/systemd/system/frpc.service
      ```
    - 粘贴以下内容 (注意修改路径和用户名)：
      ```ini
      [Unit]
      Description=FRP Client
      After=network.target

      [Service]
      Type=simple
      User=<你的内网服务器用户名>
      Restart=on-failure
      RestartSec=5s
      ExecStart=/home/<USER>/frp/frpc -c /home/<USER>/frp/frpc.toml

      [Install]
      WantedBy=multi-user.target
      ```
    - 启动并设置开机自启：
      ```bash
      sudo systemctl enable frpc
      sudo systemctl start frpc
      sudo systemctl status frpc
      ```

---

## 4. 验证与访问

1.  **访问FRP管理后台**:
    - 打开浏览器, 输入 `http://frp.your-domain.com:10795`
    - 使用你在 `frps.toml` 中设置的 `webServer` 用户名和密码登录。
    - 你应该能在 "Proxies" 列表中看到你配置的 "my-web-app" 和 "my-ssh-service" 状态为 `online`。

2.  **访问内网Web服务**:
    - 打开浏览器, 输入你的域名, 如 `https://your-domain.com` (如果Cloudflare开启了HTTPS)。
    - 你应该能看到你内网 `127.0.0.1:8080` 上的网页内容。

3.  **访问内网SSH服务**:
    - 打开终端, 使用以下命令连接:
      ```bash
      ssh <你的内网用户名>@frp.your-domain.com -p 10722
      ```

---

## 5. 附录：知识扩展

*   **关于HTTPS**: 在本指南的架构下，你无需在内网服务器上配置SSL证书。Cloudflare的橙色云代理会自动为你网站的访问者提供HTTPS加密（浏览器到Cloudflare之间）。这是最简单、最便捷的HTTPS实现方式。
*   **添加更多子域名**: 如果你想用 `blog.your-domain.com` 访问另一个内网服务，只需：
    1. 在Cloudflare DNS中添加一条新的`CNAME`记录: `blog` -> `frp.your-domain.com`。
    2. 在`frpc.toml`中添加一个新的 `[[proxies]]` 块，并将其 `customDomains` 设置为 `["blog.your-domain.com"]`。
*   **安全提示**: `auth.token` 是你整套系统的安全命脉，请务必设置为高强度的复杂密码，并确保 `frps` 和 `frpc` 的配置完全一致。

**恭喜你，至此已完成所有配置！**