#!/bin/bash

# ================================================================
# FRP + DDNS-GO 服务测试脚本
# 用于验证迁移后的服务状态
# ================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
FRP_PORT="9688"
DASHBOARD_PORT="7855"
DDNS_PORT="4588"
DOMAIN_NAME="vps.zhwok.top"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 测试系统服务状态
test_services() {
    log_info "检查系统服务状态..."
    
    # 测试FRP服务
    if systemctl is-active --quiet frps; then
        log_success "FRP服务器运行正常"
    else
        log_error "FRP服务器未运行"
        systemctl status frps --no-pager -l
        return 1
    fi
    
    # 测试DDNS-GO服务
    if systemctl is-active --quiet ddns-go; then
        log_success "DDNS-GO服务运行正常"
    else
        log_error "DDNS-GO服务未运行"
        systemctl status ddns-go --no-pager -l
        return 1
    fi
}

# 测试端口监听
test_ports() {
    log_info "检查端口监听状态..."
    
    # 测试FRP端口
    if netstat -tlnp | grep -q ":$FRP_PORT "; then
        log_success "FRP端口 $FRP_PORT 监听正常"
    else
        log_error "FRP端口 $FRP_PORT 未监听"
        return 1
    fi
    
    # 测试Dashboard端口
    if netstat -tlnp | grep -q ":$DASHBOARD_PORT "; then
        log_success "Dashboard端口 $DASHBOARD_PORT 监听正常"
    else
        log_error "Dashboard端口 $DASHBOARD_PORT 未监听"
        return 1
    fi
    
    # 测试DDNS-GO端口
    if netstat -tlnp | grep -q ":$DDNS_PORT "; then
        log_success "DDNS-GO端口 $DDNS_PORT 监听正常"
    else
        log_error "DDNS-GO端口 $DDNS_PORT 未监听"
        return 1
    fi
}

# 测试网络连通性
test_connectivity() {
    log_info "测试网络连通性..."
    
    # 获取本机IP
    LOCAL_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
    log_info "本机公网IP: $LOCAL_IP"
    
    # 测试Dashboard访问
    if curl -s --connect-timeout 5 http://127.0.0.1:$DASHBOARD_PORT >/dev/null; then
        log_success "FRP Dashboard 可访问"
    else
        log_warning "FRP Dashboard 访问异常"
    fi
    
    # 测试DDNS-GO访问
    if curl -s --connect-timeout 5 http://127.0.0.1:$DDNS_PORT >/dev/null; then
        log_success "DDNS-GO Web界面可访问"
    else
        log_warning "DDNS-GO Web界面访问异常"
    fi
}

# 测试DNS解析
test_dns() {
    log_info "测试DNS解析..."
    
    # 获取域名解析IP
    RESOLVED_IP=$(nslookup $DOMAIN_NAME | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null)
    
    if [[ -n "$RESOLVED_IP" ]]; then
        log_info "域名 $DOMAIN_NAME 解析到: $RESOLVED_IP"
        
        # 检查是否解析到本机
        LOCAL_IP=$(curl -s ifconfig.me)
        if [[ "$RESOLVED_IP" == "$LOCAL_IP" ]]; then
            log_success "域名解析正确，指向本机"
        else
            log_warning "域名解析到其他IP，可能DNS未完全生效"
            log_info "本机IP: $LOCAL_IP"
            log_info "解析IP: $RESOLVED_IP"
        fi
    else
        log_error "域名解析失败"
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    log_info "=========================================="
    log_info "服务信息汇总"
    log_info "=========================================="
    
    LOCAL_IP=$(curl -s ifconfig.me)
    
    echo ""
    echo "🌐 访问地址："
    echo "  FRP Dashboard: http://$LOCAL_IP:$DASHBOARD_PORT"
    echo "  DDNS-GO管理:   http://$LOCAL_IP:$DDNS_PORT"
    echo ""
    echo "🔧 服务端口："
    echo "  FRP服务端口:   $FRP_PORT"
    echo "  Dashboard端口: $DASHBOARD_PORT"
    echo "  DDNS-GO端口:   $DDNS_PORT"
    echo ""
    echo "🏠 内网连接配置："
    echo "  服务器地址: $DOMAIN_NAME (或 $LOCAL_IP)"
    echo "  服务器端口: $FRP_PORT"
    echo ""
    echo "📋 管理命令："
    echo "  查看FRP状态:    systemctl status frps"
    echo "  查看DDNS状态:   systemctl status ddns-go"
    echo "  查看FRP日志:    journalctl -u frps -f"
    echo "  查看DDNS日志:   journalctl -u ddns-go -f"
    echo "  重启FRP:        systemctl restart frps"
    echo "  重启DDNS:       systemctl restart ddns-go"
}

# 显示内网服务器配置指导
show_client_config() {
    log_info "=========================================="
    log_info "内网服务器配置指导"
    log_info "=========================================="
    
    LOCAL_IP=$(curl -s ifconfig.me)
    
    echo ""
    echo "📝 内网服务器frpc配置文件内容："
    echo ""
    cat << EOF
# FRP 客户端配置 (frpc.toml)
serverAddr = "$DOMAIN_NAME"  # 或使用IP: $LOCAL_IP
serverPort = $FRP_PORT
auth.method = "token"
auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"

[[proxies]]
name = "https-passthrough"
type = "tcp"
localIP = "127.0.0.1"
localPort = 443
remotePort = 443

[[proxies]]
name = "ssh"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 4088
EOF
    
    echo ""
    echo "🔄 内网服务器操作命令："
    echo "  1. 备份配置: sudo cp /opt/1panel/apps/frpc/frpc/data/frpc.toml /opt/1panel/apps/frpc/frpc/data/frpc.toml.backup"
    echo "  2. 编辑配置: sudo nano /opt/1panel/apps/frpc/frpc/data/frpc.toml"
    echo "  3. 重启容器: sudo docker restart 71aee2b14912"
    echo "  4. 查看日志: sudo docker logs 71aee2b14912 --tail 20"
}

# 主函数
main() {
    echo ""
    log_info "开始测试 FRP + DDNS-GO 服务状态..."
    echo ""
    
    # 执行测试
    test_services || exit 1
    test_ports || exit 1
    test_connectivity
    test_dns
    
    echo ""
    log_success "所有核心服务测试完成！"
    
    # 显示信息
    show_service_info
    show_client_config
    
    echo ""
    log_info "测试完成！如果所有项目都显示正常，可以开始配置内网服务器连接。"
}

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用: sudo $0"
    exit 1
fi

# 执行主函数
main "$@"
