连接谷歌云服务器
ssh -i C:\Users\<USER>\.ssh\id_rsa <EMAIL> -p 2222

连接本地服务器
ssh -i C:\Users\<USER>\.ssh\id_rsa <EMAIL> -p 4088

连接服务器的某个端口，如云服务器的ddns-go
ssh -i C:\Users\<USER>\.ssh\id_rsa -L 4588:127.0.0.1:4588 <EMAIL> -p 2222

一次建立多个隧道
ssh -i C:\Users\<USER>\.ssh\id_rsa `
-L 3306:127.0.0.1:3306 `
-L 8089:127.0.0.1:8089 `
-L 3000:127.0.0.1:3000 `
-L 8001:127.0.0.1:8001 `
-L 3015:127.0.0.1:3015 `
<EMAIL> -p 4088