# Google Vertex AI 调用示例

## Gemini 模型调用示例
以下代码演示了如何使用 Google Vertex AI 调用 Gemini 大模型：

```python
# 导入必要库
from google import genai
from google.genai.types import HttpOptions
import vertexai
from vertexai.generative_models import GenerativeModel

# 初始化Vertex AI服务
# 注意：需要提前在Google Cloud Console创建项目并启用API
project_id = "gen-lang-client-0270553108"  # 替换为实际项目ID
location = "us-central1"  # 服务区域，根据需求修改

# 初始化配置
vertexai.init(project=project_id, location=location)

# 创建Gemini模型实例
# 支持的模型版本：gemini-2.5-pro-preview-05-06 / gemini-pro 等
model = GenerativeModel("gemini-2.5-pro-preview-05-06")

# 生成内容请求
# 可传入文本、图片等多模态内容
response = model.generate_content("How does AI work?")

# 输出生成结果
print(response.text)
```

## 注意事项
1. 需要有效的Google Cloud项目并启用Vertex AI API
2. 需要配置有效的认证凭据（如service account）
3. 网络环境需允许访问Google API服务端点
4. 生成内容长度受模型上下文窗口限制