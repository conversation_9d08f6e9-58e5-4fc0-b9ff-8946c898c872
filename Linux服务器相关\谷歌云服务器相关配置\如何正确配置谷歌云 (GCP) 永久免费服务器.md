>控制台URL： https://console.cloud.google.com/compute/instancesDetail/zones/us-west1-a/instances/instance-20250605-110940?inv=1&invt=AbzWVQ&project=gen-lang-client-0270553108



本文档旨在指导用户如何正确申请和配置 Google Cloud Platform (GCP) 的永久免费服务器，以避免产生非预期的费用。结合 Google 的免费套餐政策，用户可以获得一台拥有 `1核心`、`1G内存`、`30G磁盘` 和 `每月200G标准网络流量` 的云服务器。

## 准备工作

在开始之前，请确保您已准备好以下几点：

1.  **Google 账户**：一个全新的或已有的 Google 账户。
2.  **支付方式**：建议准备一张虚拟信用卡并绑定到您的 Google 账户，可以将额度设置为零，以防止试用期结束后被意外扣费。
3.  **干净的网络环境**：操作时建议开启浏览器的无痕模式。
4.  **（可选）美国虚拟身份信息**：如果需要，可以通过在线生成器获取。

## 永久免费的关键配置条件

为了确保您的服务器实例能够永久免费运行，**必须严格遵守**以下所有配置要求。任何一项偏离都可能导致收费。

### 1. 实例数量
只能创建 **一台** 免费的服务器实例。创建超过一台将会产生费用。

### 2. 账户资格
*   新老 Google 账户均可申请。
*   即使是度过了3个月（90天）免费试用期的老账号，在升级为付费账号后，依然可以享有此永久免费套餐。

### 3. 服务器区域 (Region)
地区选择**必须**是以下三个美国区域之一：
*   俄勒冈州：`us-west1`
*   艾奥瓦州：`us-central1`
*   南卡罗来纳州：`us-east1`

![GCP 免费区域选择](https://www.freedidi.com/wp-content/uploads/2024/11/1ecaf43c1d20241104154832.webp)

### 4. 机器配置 (Machine Configuration)
*   **系列 (Series)**：必须选择 `E2`。
*   **机器类型 (Machine Type)**：必须选择 `e2-micro`（共享核心，1个vCPU，1GB内存）。

![GCP 机器配置选择](https://www.freedidi.com/wp-content/uploads/2024/11/4a23c750a420241104154629.webp)

### 5. 启动磁盘 (Boot Disk)
*   **类型 (Type)**：必须选择 `标准永久性磁盘 (Standard persistent disk)`。
*   **大小 (Size)**：必须**恰好为 30 GB**。任何其他大小（无论更大或更小）都会导致收费。

![GCP 磁盘选择](https://www.freedidi.com/wp-content/uploads/2024/11/1fd8412fdd20241104155042.webp)

### 6. 网络服务层级 (Network Service Tiers)
这是**最关键且最容易出错**的一步。
*   在 "网络" -> "网络接口" 设置中，找到 **网络服务层级 (Network Service Tier)**。
*   **必须**将其从默认的 "高级 (Premium)" 修改为 **"标准 (Standard)"**。
*   只有选择了 **标准层级**，才能享受每月 200 GB 的免费出站流量。高级层级会立即产生费用。

![GCP 网络层级选择](https://www.freedidi.com/wp-content/uploads/2024/11/ef57b0311620241104151714.webp)

![GCP 网络接口配置详情](https://www.freedidi.com/wp-content/uploads/2024/11/d1682fd61b20241104152826-2560x1077.webp)
