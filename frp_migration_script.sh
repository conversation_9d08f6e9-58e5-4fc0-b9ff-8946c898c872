#!/bin/bash

# ================================================================
# FRP + DDNS-GO 一键迁移脚本 (谷歌云 → 腾讯云)
# 安全版本 - 包含测试和回滚机制
# ================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 (从谷歌云服务器分析得出)
FRP_VERSION="0.63.0"
FRP_TOKEN="mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"
FRP_PORT="9688"
DASHBOARD_PORT="7855"
DASHBOARD_USER="zhw"
DASHBOARD_PASS="Zhwok949!@%+"
DDNS_PORT="4588"
DOMAIN_NAME="vps.zhwok.top"

# 获取腾讯云服务器的公网IP
TENCENT_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 系统环境检查
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法识别操作系统"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查架构
    ARCH=$(uname -m)
    case $ARCH in
        x86_64) FRP_ARCH="amd64" ;;
        aarch64) FRP_ARCH="arm64" ;;
        *) log_error "不支持的架构: $ARCH"; exit 1 ;;
    esac
    
    log_info "系统架构: $ARCH -> $FRP_ARCH"
    log_info "腾讯云服务器IP: $TENCENT_IP"
}

# 安装依赖
install_dependencies() {
    log_info "安装必要依赖..."
    
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update
        apt-get install -y wget curl unzip systemctl
    elif command -v yum >/dev/null 2>&1; then
        yum update -y
        yum install -y wget curl unzip systemd
    else
        log_error "不支持的包管理器"
        exit 1
    fi
}

# 创建工作目录
create_directories() {
    log_info "创建工作目录..."
    
    mkdir -p /opt/frp
    mkdir -p /opt/ddns-go
    mkdir -p /etc/frp
    mkdir -p /var/log/frp
}

# 下载并安装FRP
install_frp() {
    log_info "下载并安装 FRP $FRP_VERSION..."
    
    cd /opt/frp
    
    # 下载FRP
    FRP_URL="https://github.com/fatedier/frp/releases/download/v${FRP_VERSION}/frp_${FRP_VERSION}_linux_${FRP_ARCH}.tar.gz"
    log_info "下载地址: $FRP_URL"
    
    if ! wget -O frp.tar.gz "$FRP_URL"; then
        log_error "FRP下载失败"
        exit 1
    fi
    
    # 解压
    tar -xzf frp.tar.gz
    mv frp_${FRP_VERSION}_linux_${FRP_ARCH}/* .
    rm -rf frp_${FRP_VERSION}_linux_${FRP_ARCH} frp.tar.gz
    
    # 设置权限
    chmod +x frps frpc
    
    log_success "FRP安装完成"
}

# 配置FRP服务器
configure_frps() {
    log_info "配置 FRP 服务器..."
    
    cat > /etc/frp/frps.toml << EOF
# ==============================
# FRP 服务器端 (frps.toml) - 腾讯云迁移版本
# ==============================

# 服务器监听端口，供 frpc 客户端连接
bindPort = $FRP_PORT

# Web 控制台（Dashboard）配置
webServer.addr = "127.0.0.1"
webServer.port = $DASHBOARD_PORT
webServer.user = "$DASHBOARD_USER"
webServer.password = "$DASHBOARD_PASS"

# 身份验证
auth.method = "token"
auth.token = "$FRP_TOKEN"
EOF
    
    log_success "FRP服务器配置完成"
}

# 创建FRP systemd服务
create_frps_service() {
    log_info "创建 FRP systemd 服务..."
    
    cat > /etc/systemd/system/frps.service << EOF
[Unit]
Description=FRP Server Service
After=network.target

[Service]
Type=simple
ExecStart=/opt/frp/frps -c /etc/frp/frps.toml
Restart=on-failure
RestartSec=5s
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable frps
    
    log_success "FRP服务创建完成"
}

# 下载并安装DDNS-GO
install_ddns_go() {
    log_info "下载并安装 DDNS-GO..."
    
    cd /opt/ddns-go
    
    # 获取最新版本
    DDNS_VERSION=$(curl -s https://api.github.com/repos/jeessy2/ddns-go/releases/latest | grep '"tag_name"' | cut -d'"' -f4)
    
    if [[ -z "$DDNS_VERSION" ]]; then
        log_warning "无法获取DDNS-GO最新版本，使用默认版本"
        DDNS_VERSION="v6.7.5"
    fi
    
    log_info "DDNS-GO版本: $DDNS_VERSION"
    
    # 下载DDNS-GO
    DDNS_URL="https://github.com/jeessy2/ddns-go/releases/download/${DDNS_VERSION}/ddns-go_${DDNS_VERSION#v}_linux_${FRP_ARCH}.tar.gz"
    
    if ! wget -O ddns-go.tar.gz "$DDNS_URL"; then
        log_error "DDNS-GO下载失败"
        exit 1
    fi
    
    # 解压并安装
    tar -xzf ddns-go.tar.gz
    cp ddns-go /usr/local/bin/
    chmod +x /usr/local/bin/ddns-go
    rm -f ddns-go.tar.gz ddns-go
    
    log_success "DDNS-GO安装完成"
}

# 创建DDNS-GO systemd服务
create_ddns_service() {
    log_info "创建 DDNS-GO systemd 服务..."
    
    cat > /etc/systemd/system/ddns-go.service << EOF
[Unit]
Description=DDNS-GO Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/ddns-go
ExecStart=/usr/local/bin/ddns-go -l :$DDNS_PORT
Restart=on-failure
RestartSec=5s
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable ddns-go
    
    log_success "DDNS-GO服务创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查防火墙类型并配置
    if command -v ufw >/dev/null 2>&1; then
        ufw allow $FRP_PORT/tcp
        ufw allow $DDNS_PORT/tcp
        ufw allow 443/tcp
        ufw allow 4088/tcp
        log_info "UFW防火墙规则已添加"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        firewall-cmd --permanent --add-port=$FRP_PORT/tcp
        firewall-cmd --permanent --add-port=$DDNS_PORT/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --permanent --add-port=4088/tcp
        firewall-cmd --reload
        log_info "Firewalld防火墙规则已添加"
    else
        log_warning "未检测到防火墙，请手动开放端口: $FRP_PORT, $DDNS_PORT, 443, 4088"
    fi
}

# 启动服务（测试模式）
start_services_test() {
    log_info "启动服务进行测试..."
    
    # 启动FRP服务器
    systemctl start frps
    sleep 3
    
    if systemctl is-active --quiet frps; then
        log_success "FRP服务器启动成功"
    else
        log_error "FRP服务器启动失败"
        systemctl status frps
        exit 1
    fi
    
    # 启动DDNS-GO服务
    systemctl start ddns-go
    sleep 3
    
    if systemctl is-active --quiet ddns-go; then
        log_success "DDNS-GO服务启动成功"
    else
        log_error "DDNS-GO服务启动失败"
        systemctl status ddns-go
        exit 1
    fi
}

# 测试服务连通性
test_connectivity() {
    log_info "测试服务连通性..."
    
    # 测试FRP端口
    if netstat -tlnp | grep -q ":$FRP_PORT "; then
        log_success "FRP端口 $FRP_PORT 监听正常"
    else
        log_error "FRP端口 $FRP_PORT 监听异常"
        exit 1
    fi
    
    # 测试DDNS-GO端口
    if netstat -tlnp | grep -q ":$DDNS_PORT "; then
        log_success "DDNS-GO端口 $DDNS_PORT 监听正常"
    else
        log_error "DDNS-GO端口 $DDNS_PORT 监听异常"
        exit 1
    fi
    
    # 测试Dashboard
    if curl -s http://127.0.0.1:$DASHBOARD_PORT >/dev/null; then
        log_success "FRP Dashboard 可访问"
    else
        log_warning "FRP Dashboard 可能未完全启动，请稍后检查"
    fi
}

# 显示迁移指导
show_migration_guide() {
    log_info "=========================================="
    log_success "腾讯云服务器配置完成！"
    log_info "=========================================="
    
    echo -e "${YELLOW}下一步迁移指导：${NC}"
    echo ""
    echo "1. 【重要】先测试连接："
    echo "   在内网服务器上临时修改frpc配置测试连接："
    echo "   serverAddr = \"$TENCENT_IP\"  # 使用腾讯云IP测试"
    echo ""
    echo "2. 测试成功后，配置DDNS-GO："
    echo "   访问: http://$TENCENT_IP:$DDNS_PORT"
    echo "   配置域名 $DOMAIN_NAME 指向 $TENCENT_IP"
    echo ""
    echo "3. 等待DNS生效后，修改内网frpc配置："
    echo "   serverAddr = \"$DOMAIN_NAME\"  # 使用域名"
    echo ""
    echo "4. 确认一切正常后，关闭谷歌云服务器"
    echo ""
    echo -e "${RED}警告：请务必先测试，确保连接正常后再完全切换！${NC}"
    echo ""
    echo "服务状态检查命令："
    echo "  systemctl status frps"
    echo "  systemctl status ddns-go"
    echo ""
    echo "日志查看命令："
    echo "  journalctl -u frps -f"
    echo "  journalctl -u ddns-go -f"
}

# 主函数
main() {
    log_info "开始 FRP + DDNS-GO 迁移部署..."
    
    check_root
    check_system
    install_dependencies
    create_directories
    install_frp
    configure_frps
    create_frps_service
    install_ddns_go
    create_ddns_service
    configure_firewall
    start_services_test
    test_connectivity
    show_migration_guide
    
    log_success "部署完成！请按照上述指导进行安全迁移。"
}

# 执行主函数
main "$@"
