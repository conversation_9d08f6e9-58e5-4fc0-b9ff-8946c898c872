#!/bin/bash

# ================================================================
# 紧急回滚脚本 - 内网服务器连接恢复
# 当迁移失败时，快速恢复内网服务器到谷歌云的连接
# ================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
GOOGLE_CLOUD_IP="**************"
GOOGLE_CLOUD_DOMAIN="vps.zhwok.top"
FRPC_CONFIG_PATH="/opt/1panel/apps/frpc/frpc/data/frpc.toml"
FRPC_BACKUP_PATH="/opt/1panel/apps/frpc/frpc/data/frpc.toml.backup"
FRPC_CONTAINER_ID="71aee2b14912"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在内网服务器上运行
check_environment() {
    log_info "检查运行环境..."
    
    # 检查是否存在frpc配置文件
    if [[ ! -f "$FRPC_CONFIG_PATH" ]]; then
        log_error "未找到frpc配置文件: $FRPC_CONFIG_PATH"
        log_error "请确认在内网服务器上运行此脚本"
        exit 1
    fi
    
    # 检查Docker容器
    if ! docker ps | grep -q "$FRPC_CONTAINER_ID"; then
        log_warning "未找到frpc容器 $FRPC_CONTAINER_ID"
        log_info "尝试查找frpc容器..."
        
        CONTAINER_ID=$(docker ps | grep frpc | awk '{print $1}' | head -1)
        if [[ -n "$CONTAINER_ID" ]]; then
            log_info "找到frpc容器: $CONTAINER_ID"
            FRPC_CONTAINER_ID="$CONTAINER_ID"
        else
            log_error "未找到运行中的frpc容器"
            exit 1
        fi
    fi
    
    log_success "环境检查完成"
}

# 显示当前配置
show_current_config() {
    log_info "当前frpc配置:"
    echo "----------------------------------------"
    head -10 "$FRPC_CONFIG_PATH"
    echo "----------------------------------------"
}

# 方案1: 恢复备份配置
restore_backup() {
    log_info "方案1: 恢复备份配置..."
    
    if [[ -f "$FRPC_BACKUP_PATH" ]]; then
        log_info "找到备份文件，正在恢复..."
        cp "$FRPC_BACKUP_PATH" "$FRPC_CONFIG_PATH"
        log_success "配置文件已恢复"
        return 0
    else
        log_warning "未找到备份文件: $FRPC_BACKUP_PATH"
        return 1
    fi
}

# 方案2: 手动配置谷歌云连接
configure_google_cloud() {
    log_info "方案2: 配置连接到谷歌云服务器..."
    
    # 备份当前配置
    cp "$FRPC_CONFIG_PATH" "${FRPC_CONFIG_PATH}.emergency.$(date +%Y%m%d_%H%M%S)"
    
    # 创建谷歌云配置
    cat > "$FRPC_CONFIG_PATH" << 'EOF'
# FRP 客户端配置 (紧急恢复版本)
# 连接到谷歌云服务器

serverAddr = "vps.zhwok.top"  # 谷歌云域名
serverPort = 9688
auth.method = "token"
auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"

[[proxies]]
name = "https-passthrough"
type = "tcp"
localIP = "127.0.0.1"
localPort = 443
remotePort = 443

[[proxies]]
name = "ssh"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 4088
EOF
    
    log_success "谷歌云配置已写入"
}

# 方案3: 使用IP直连
configure_ip_connection() {
    log_info "方案3: 使用谷歌云IP直连..."
    
    # 备份当前配置
    cp "$FRPC_CONFIG_PATH" "${FRPC_CONFIG_PATH}.emergency.$(date +%Y%m%d_%H%M%S)"
    
    # 创建IP直连配置
    cat > "$FRPC_CONFIG_PATH" << EOF
# FRP 客户端配置 (IP直连版本)
# 直接连接到谷歌云IP

serverAddr = "$GOOGLE_CLOUD_IP"  # 谷歌云IP
serverPort = 9688
auth.method = "token"
auth.token = "mbO1vxLpRwACJyXhfJ3K0mKK0wSVoVo3GDM0UypgLPC"

[[proxies]]
name = "https-passthrough"
type = "tcp"
localIP = "127.0.0.1"
localPort = 443
remotePort = 443

[[proxies]]
name = "ssh"
type = "tcp"
localIP = "127.0.0.1"
localPort = 22
remotePort = 4088
EOF
    
    log_success "IP直连配置已写入"
}

# 重启frpc容器
restart_frpc() {
    log_info "重启frpc容器..."
    
    docker restart "$FRPC_CONTAINER_ID"
    sleep 5
    
    # 检查容器状态
    if docker ps | grep -q "$FRPC_CONTAINER_ID"; then
        log_success "frpc容器重启成功"
    else
        log_error "frpc容器重启失败"
        return 1
    fi
}

# 测试连接
test_connection() {
    log_info "测试连接状态..."
    
    # 等待连接建立
    sleep 10
    
    # 检查容器日志
    log_info "检查连接日志..."
    docker logs "$FRPC_CONTAINER_ID" --tail 20
    
    # 检查是否有成功连接的日志
    if docker logs "$FRPC_CONTAINER_ID" --tail 50 | grep -q "login to server success"; then
        log_success "连接成功！"
        return 0
    else
        log_warning "未检测到成功连接日志"
        return 1
    fi
}

# 显示状态信息
show_status() {
    log_info "=========================================="
    log_info "当前状态信息"
    log_info "=========================================="
    
    echo ""
    echo "📋 容器状态:"
    docker ps | grep frpc
    
    echo ""
    echo "📝 当前配置:"
    head -5 "$FRPC_CONFIG_PATH"
    
    echo ""
    echo "📊 最新日志:"
    docker logs "$FRPC_CONTAINER_ID" --tail 10
}

# 交互式恢复
interactive_recovery() {
    log_info "=========================================="
    log_error "检测到连接问题，启动紧急恢复程序"
    log_info "=========================================="
    
    show_current_config
    
    echo ""
    echo "请选择恢复方案:"
    echo "1) 恢复备份配置 (推荐)"
    echo "2) 配置谷歌云域名连接"
    echo "3) 配置谷歌云IP直连"
    echo "4) 显示当前状态"
    echo "5) 退出"
    
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            if restore_backup; then
                restart_frpc && test_connection
            else
                log_error "备份恢复失败，尝试其他方案"
                interactive_recovery
            fi
            ;;
        2)
            configure_google_cloud
            restart_frpc && test_connection
            ;;
        3)
            configure_ip_connection
            restart_frpc && test_connection
            ;;
        4)
            show_status
            interactive_recovery
            ;;
        5)
            log_info "退出恢复程序"
            exit 0
            ;;
        *)
            log_error "无效选择，请重新选择"
            interactive_recovery
            ;;
    esac
}

# 自动恢复
auto_recovery() {
    log_info "开始自动恢复..."
    
    # 尝试方案1: 恢复备份
    if restore_backup; then
        restart_frpc
        if test_connection; then
            log_success "自动恢复成功！"
            return 0
        fi
    fi
    
    # 尝试方案2: 域名连接
    log_info "尝试域名连接..."
    configure_google_cloud
    restart_frpc
    if test_connection; then
        log_success "域名连接恢复成功！"
        return 0
    fi
    
    # 尝试方案3: IP直连
    log_info "尝试IP直连..."
    configure_ip_connection
    restart_frpc
    if test_connection; then
        log_success "IP直连恢复成功！"
        return 0
    fi
    
    log_error "自动恢复失败，启动交互式恢复"
    interactive_recovery
}

# 主函数
main() {
    echo ""
    log_error "🚨 FRP连接紧急恢复脚本 🚨"
    echo ""
    
    check_environment
    
    # 检查参数
    if [[ "$1" == "--auto" ]]; then
        auto_recovery
    else
        interactive_recovery
    fi
    
    show_status
    
    echo ""
    log_info "恢复程序完成"
    log_info "如果仍有问题，请检查:"
    log_info "1. 谷歌云服务器是否正常运行"
    log_info "2. 网络连接是否正常"
    log_info "3. 防火墙设置是否正确"
}

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用: sudo $0"
    exit 1
fi

# 执行主函数
main "$@"
