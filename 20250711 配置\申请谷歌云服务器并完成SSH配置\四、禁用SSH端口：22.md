将名称为：[default-allow-ssh](https://console.cloud.google.com/net-security/firewall-manager/firewall-policies/details/default-allow-ssh?project=gen-lang-client-0270553108&inv=1&invt=Ab2eew) 和 [allow-iap-ssh](https://console.cloud.google.com/net-security/firewall-manager/firewall-policies/details/allow-iap-ssh?project=gen-lang-client-0270553108&inv=1&invt=Ab2eew) 的防火墙协议/端口的“22”端口删除，禁用22端口连接SSH
万一以后SSH失败，还可以再次添加22端口，用谷歌内置的SSH来连接