## 系统监控命令
- `top`：实时查看系统资源使用情况，包括CPU、内存使用率和运行进程

## 软件包管理
- `apt update`：更新软件包索引
- `apt install <包名>`：安装软件包，如vim、ufw、netcat等
- 安装软件需要root权限，使用`sudo`提升权限
- `apt-get update`：旧版更新软件包索引命令

## frp服务器部署步骤
1. 下载frp软件包：
   ```
   wget https://github.com/fatedier/frp/releases/download/v0.62.1/frp_0.62.1_linux_amd64.tar.gz
   ```

2. 解压软件包：
   ```
   tar -zxvf frp_0.62.1_linux_amd64.tar.gz
   ```

3. 移动frps至可执行目录并创建配置目录：
   ```
   sudo mv frps /usr/local/bin/
   sudo mkdir -p /etc/frp
   sudo cp frps.toml /etc/frp/
   ```

4. 编辑frps配置文件：
   ```
   sudo vim /etc/frp/frps.toml
   ```
   配置内容示例：
   ```
   bindPort = 9490
   auth.token="ZHWok949!@%+"
   
   webServer.addr = "0.0.0.0"
   webServer.port = 10794
   webServer.user = "zhwok949"
   webServer.password = "ZHWok949!@%*+"
   ```

5. 创建systemd服务单元：
   ```
   sudo bash -c 'cat > /etc/systemd/system/frps.service << EOF
   [Unit]
   Description=frps service
   After=network.target
   
   [Service]
   ExecStart=/usr/local/bin/frps -c /etc/frp/frps.toml
   Restart=on-failure
   
   [Install]
   WantedBy=multi-user.target
   EOF'
   ```

6. 启动并设置开机自启：
   ```
   sudo systemctl daemon-reload
   sudo systemctl start frps
   sudo systemctl enable frps
   ```

7. 查看服务状态：
   ```
   sudo systemctl status frps
   ```

## 防火墙配置
1. 安装ufw：
   ```
   sudo apt install ufw
   ```

2. 开放frp服务端口：
   ```
   sudo ufw allow 9490/tcp
   sudo ufw allow 10794/tcp
   ```

3. 启用防火墙：
   ```
   sudo ufw enable
   ```

4. 查看防火墙状态：
   ```
   sudo ufw status
   ```

## 网络测试
- 安装netcat：`sudo apt install netcat`
- 测试端口连接：`nc -zv localhost <端口号>`

## 常见问题解决
- 权限不足错误：使用`sudo`提升权限
- 命令未找到：先使用`apt install`安装相应软件包
- 服务无法启动：检查配置文件和日志，使用`systemctl status`查看状态