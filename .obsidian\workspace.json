{"main": {"id": "f891385a5985900e", "type": "split", "children": [{"id": "91d5a78369ee78aa", "type": "tabs", "children": [{"id": "2ef7a83da0d253d1", "type": "leaf", "state": {"type": "obsidian-projects", "state": {}, "icon": "projects-icon", "title": "Projects"}}]}], "direction": "vertical"}, "left": {"id": "3fd7ccff48712b54", "type": "split", "children": [{"id": "96df4ff5daab82b9", "type": "tabs", "children": [{"id": "4ee0edb786e89619", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "d72203d4280b1dd2", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "dc458d49edc11af9", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 411.5}, "right": {"id": "aa6ef483859a0114", "type": "split", "children": [{"id": "b0d32b887df7fb81", "type": "tabs", "children": [{"id": "9d9fb12676eb7743", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "1d1b4c6cba4b85bf", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "a750cefd821f6326", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "bd2e9b93553426cc", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "大纲"}}, {"id": "b06daf6c52873fde", "type": "leaf", "state": {"type": "git-view", "state": {}, "icon": "git-pull-request", "title": "Source Control"}}], "currentTab": 4}], "direction": "horizontal", "width": 658.5}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-git:Open Git source control": false, "obsidian-projects:打开项目": false}}, "active": "2ef7a83da0d253d1", "lastOpenFiles": ["AI相关/谷歌API列表.md", "工作管理/2025H2/未命名 - 副本.md", "工作管理/2025H2/未命名 1.md", "工作管理/2025H2/未命名.md", "工作管理/2025.md", "工作管理/2025H2", "工作相关/提示词/成交报告提示词.md", "SMTCMP~1/chats/v1_%E6%80%BB%E7%BB%93%E4%B8%80%E4%B8%8B%E8%BF%99%E4%BB%BD%E7%AC%94%E8%AE%B0_1749527998513_3619abf6-004a-45c1-8fb7-4be021f05b54.json", "SMTCMP~1/chats/v1_%E5%B8%AE%E6%88%91%E7%9C%8B%E7%9C%8B%E8%BF%99%E4%B8%AA%E6%96%87%E4%BB%B6_1749526252340_a0fd8e1b-df0a-4d6b-8645-f6b70bd80972.json", "SMTCMP~1/chats/v1_%E5%B8%AE%E6%88%91%E5%86%99%E4%B8%8A%E6%B3%A8%E9%87%8A%EF%BC%8C%E5%B9%B6%E4%BC%98%E5%8C%96markdown%E6%A0%BC%E5%BC%8F_1749527020828_6b4e6be4-9de4-4584-9c7b-90abd9e17a18.json", "SMTCMP~1/chats/v1_%40%E8%B0%83%E7%94%A8Goolge%20Vertex%20AI.md%20%20%E6%80%BB%E7%BB%93%E8%BF%99%E4%B8%AA%E7%AC%94%E8%AE%B0_1749527911407_7f961eca-dcdd-4ed3-8e39-a90c878c8de0.json", "SMTCMP~1/chats", "Pasted image 20250712003826.png", "Pasted image 20250712003744.png", "Pasted image 20250712003550.png", "20250711 配置/配置Nginx/Nginx + SSL + Acme + Frp SSL免费证书申请+自动续期+反向代理配置 极细致教程.md", "20250711 配置/配置FRP/frps的配置.md", "20250711 配置/配置ddns-go/腾讯云API密钥.md", "20250711 配置/配置ddns-go/管理员账号密码.md", "20250711 配置/配置ddns-go/安装并配置ddns-go.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/四、禁用SSH端口：22.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/远程连接命令.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/二、在谷歌云服务器配置并开启SSH.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/二、生成SSH私钥并配置.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/一、申请谷歌云服务器.md", "20250711 配置/申请谷歌云服务器并完成SSH配置/三、更改SSH端口.md", "20250711 配置/Edgeone配置 （待修改）/在 EdgeOne 中将 DDNS 域名设置为源站.md", "20250711 配置/配置Nginx", "20250711 配置/配置FRP", "20250711 配置/配置ddns-go", "20250711 配置/远程连接SSH.md", "20250711 配置/申请谷歌云服务器并完成SSH配置", "Obsidian/20250711 配置/配置FRP/frps的配置.md", "Obsidian/20250711 配置/配置Nginx/Nginx + SSL + Acme + Frp SSL免费证书申请+自动续期+反向代理配置 极细致教程.md", "Obsidian/20250711 配置/申请谷歌云服务器并完成SSH配置/远程连接命令.md", "Obsidian/20250711 配置/申请谷歌云服务器并完成SSH配置/四、禁用SSH端口：22.md", "Obsidian/20250711 配置/申请谷歌云服务器并完成SSH配置/二、生成SSH私钥并配置.md", "Obsidian/20250711 配置/申请谷歌云服务器并完成SSH配置/二、在谷歌云服务器配置并开启SSH.md", "Obsidian/20250711 配置/申请谷歌云服务器并完成SSH配置/三、更改SSH端口.md", "Obsidian/Pasted image 20250712003826.png", "Obsidian/Pasted image 20250712003744.png", "Obsidian/Pasted image 20250712003550.png"]}